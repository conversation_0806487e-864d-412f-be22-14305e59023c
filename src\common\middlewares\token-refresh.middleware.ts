import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class TokenRefreshMiddleware implements NestMiddleware {
  constructor(
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {}

  async use(req: Request, res: Response, next: NextFunction) {
    const token = req.cookies['access_token'];

    if (token) {
      try {
        const decodedToken: any = this.jwtService.verify(token, {
          ignoreExpiration: true,
        });

        const expirationThreshold = 10; // seconds
        if (decodedToken.exp - Date.now() / 1000 < expirationThreshold) {
          const newToken = await this.issueNewToken(
            decodedToken.sub,
            decodedToken.email,
            decodedToken.roleId,
            decodedToken.companyId,
          );
          res.cookie('access_token', newToken, {
            httpOnly: true,
            maxAge: this.configService.get('JWT_EXPIRES_SECONDS') * 1000,
          }); // Convert to milliseconds
        }
      } catch (error) {
        console.error('Token verification error:', error);
      }
    }

    next();
  }

  private async issueNewToken(
    sub: string,
    email: string,
    roleId: string,
    companyId: string,
  ): Promise<string> {
    const payload = { sub, email, roleId, companyId };
    return await this.jwtService.signAsync(payload);
  }
}
